using SmaTrendFollower.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class RiskManagerTests
{
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IAlpacaTradingClient> _mockTradingClient;
    private readonly Mock<ILogger<RiskManager>> _mockLogger;
    private readonly RiskManager _riskManager;

    public RiskManagerTests()
    {
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockTradingClient = new Mock<IAlpacaTradingClient>();
        _mockLogger = new Mock<ILogger<RiskManager>>();

        // Set up rate limit helper
        var mockRateLimitHelper = new Mock<IAlpacaRateLimitHelper>();
        mockRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<decimal>>>(), It.IsAny<string>()))
            .Returns<Func<Task<decimal>>, string>((func, key) => func());

        _mockClientFactory.Setup(x => x.CreateTradingClient()).Returns(_mockTradingClient.Object);
        _mockClientFactory.Setup(x => x.GetRateLimitHelper()).Returns(mockRateLimitHelper.Object);

        _riskManager = new RiskManager(_mockClientFactory.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithValidSignal_ReturnsCorrectQuantity()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(100000m);
        
        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var quantity = await _riskManager.CalculateQuantityAsync(signal);

        // Assert
        quantity.Should().BeGreaterThan(0);
        // Risk dollars = min(100000 * 0.01, 1000) = 1000
        // Quantity = 1000 / (3 * 150) = 2.22
        // Max quantity = 1000 / 150 = 6.67
        // Final quantity = min(2.22, 6.67) = 2.22
        quantity.Should().BeApproximately(2.22m, 0.01m);
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithHighEquity_CapsRiskAt1000()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 100m, 2m, 0.10m);
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(1000000m); // $1M equity
        
        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var quantity = await _riskManager.CalculateQuantityAsync(signal);

        // Assert
        // Risk dollars should be capped at 1000, not 10000 (1% of 1M)
        // Quantity = 1000 / (2 * 100) = 5
        quantity.Should().BeApproximately(5m, 0.01m);
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithNullEquity_ReturnsZero()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns((decimal?)null);
        
        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var quantity = await _riskManager.CalculateQuantityAsync(signal);

        // Assert
        quantity.Should().Be(0);
    }
}
