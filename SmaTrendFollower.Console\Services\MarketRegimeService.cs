using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Models;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for detecting and caching market regime information based on SPY analysis
/// </summary>
public sealed class MarketRegimeService : IMarketRegimeService, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IDatabase? _redis;
    private readonly ConnectionMultiplexer? _connectionMultiplexer;
    private readonly ILogger<MarketRegimeService> _logger;
    
    // Regime detection thresholds
    private const decimal TrendingSlopeThreshold = 0.05m; // Lowered from 0.2m to better detect trends
    private const decimal VolatileAtrThreshold = 3.0m;
    private const decimal MinReturnToDrawdownRatio = 1.0m; // Lowered from 1.5m to better detect clear trends
    private const int AnalysisPeriodDays = 100;
    private const int SmaLookbackDays = 5; // For slope calculation
    
    public MarketRegimeService(
        IMarketDataService marketDataService,
        IConfiguration configuration,
        ILogger<MarketRegimeService> logger)
    {
        _marketDataService = marketDataService;
        _logger = logger;
        
        // Initialize Redis connection if configured (optional)
        try
        {
            var redisUrl = configuration["REDIS_URL"];
            if (!string.IsNullOrEmpty(redisUrl))
            {
                var redisDatabase = int.Parse(configuration["REDIS_DATABASE"] ?? "0");
                var redisPassword = configuration["REDIS_PASSWORD"];

                var configOptions = ConfigurationOptions.Parse(redisUrl);
                configOptions.AbortOnConnectFail = false; // Don't fail if Redis is unavailable
                if (!string.IsNullOrEmpty(redisPassword))
                {
                    configOptions.Password = redisPassword;
                }

                _connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
                _redis = _connectionMultiplexer.GetDatabase(redisDatabase);
                _logger.LogInformation("MarketRegimeService initialized with Redis database {Database}", redisDatabase);
            }
            else
            {
                _logger.LogInformation("Redis not configured - market regime caching disabled");
                _connectionMultiplexer = null;
                _redis = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to Redis - market regime caching disabled");
            _connectionMultiplexer = null;
            _redis = null;
        }
    }
    
    public async Task<MarketRegime> DetectRegimeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting market regime detection");
            
            // Get SPY historical data for analysis
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-AnalysisPeriodDays);
            
            var spyBars = await _marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
            var barsList = spyBars.Items.ToList();
            
            if (barsList.Count < 50)
            {
                _logger.LogWarning("Insufficient SPY data for regime analysis, defaulting to Sideways");
                return MarketRegime.Sideways;
            }
            
            // Calculate metrics for regime detection
            var closes = barsList.Select(b => (decimal)b.Close).ToList();
            var highs = barsList.Select(b => (decimal)b.High).ToList();
            var lows = barsList.Select(b => (decimal)b.Low).ToList();
            
            // Calculate 200-day SMA slope
            var smaSlope = CalculateSmaSlope(closes);
            
            // Calculate average ATR
            var averageAtr = CalculateAverageAtr(highs, lows, closes);
            
            // Calculate return-to-drawdown ratio
            var returnToDrawdownRatio = CalculateReturnToDrawdownRatio(closes);
            
            // Determine regime based on metrics
            var regime = ClassifyRegime(smaSlope, averageAtr, returnToDrawdownRatio);
            
            // Calculate confidence score
            var confidence = CalculateConfidence(smaSlope, averageAtr, returnToDrawdownRatio);
            
            // Cache the result in Redis if available
            if (_redis != null)
            {
                try
                {
                    var regimeData = new RedisMarketRegime
                    {
                        Regime = regime,
                        DetectedAt = DateTime.UtcNow,
                        SmaSlope = smaSlope,
                        AverageAtr = averageAtr,
                        ReturnToDrawdownRatio = returnToDrawdownRatio,
                        Confidence = confidence,
                        Metadata = $"Analyzed {barsList.Count} bars from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
                    };

                    await _redis.StringSetAsync(RedisMarketRegime.GetRedisKey(), regimeData.ToJson(), TimeSpan.FromDays(1));
                    _logger.LogDebug("Market regime cached in Redis");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to cache market regime in Redis");
                }
            }
            
            _logger.LogInformation("Market regime detected: {Regime} (Confidence: {Confidence:P1}, Slope: {Slope:F3}, ATR: {Atr:F2}, R2D: {R2D:F2})",
                regime, confidence, smaSlope, averageAtr, returnToDrawdownRatio);
                
            return regime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting market regime, defaulting to Sideways");
            return MarketRegime.Sideways;
        }
    }
    
    public async Task<MarketRegime> GetCachedRegimeAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            _logger.LogDebug("Redis not available, detecting new regime");
            return await DetectRegimeAsync(cancellationToken);
        }

        try
        {
            var regimeJson = await _redis.StringGetAsync(RedisMarketRegime.GetRedisKey());
            if (!regimeJson.HasValue)
            {
                _logger.LogInformation("No cached regime found, detecting new regime");
                return await DetectRegimeAsync(cancellationToken);
            }

            var regimeData = RedisMarketRegime.FromJson(regimeJson!);
            if (regimeData == null)
            {
                _logger.LogWarning("Failed to deserialize cached regime, detecting new regime");
                return await DetectRegimeAsync(cancellationToken);
            }

            _logger.LogDebug("Retrieved cached regime: {Regime} (detected at {DetectedAt})",
                regimeData.Regime, regimeData.DetectedAt);

            return regimeData.Regime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached regime, detecting new regime");
            return await DetectRegimeAsync(cancellationToken);
        }
    }
    
    public async Task<RedisMarketRegime?> GetRegimeDetailsAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return null;
        }

        try
        {
            var regimeJson = await _redis.StringGetAsync(RedisMarketRegime.GetRedisKey());
            if (!regimeJson.HasValue)
            {
                return null;
            }

            return RedisMarketRegime.FromJson(regimeJson!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving regime details");
            return null;
        }
    }
    
    public async Task<MarketRegime> RefreshRegimeAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Forcing regime refresh");
        return await DetectRegimeAsync(cancellationToken);
    }
    
    public async Task<bool> IsTradingAllowedAsync(CancellationToken cancellationToken = default)
    {
        var regime = await GetCachedRegimeAsync(cancellationToken);
        
        // Allow trading in TrendingUp and Sideways regimes
        // Block trading in TrendingDown and Volatile regimes
        var isAllowed = regime == MarketRegime.TrendingUp || regime == MarketRegime.Sideways;
        
        _logger.LogInformation("Trading allowed for regime {Regime}: {IsAllowed}", regime, isAllowed);
        
        return isAllowed;
    }
    
    private decimal CalculateSmaSlope(List<decimal> closes)
    {
        if (closes.Count < 200 + SmaLookbackDays)
        {
            return 0m;
        }
        
        // Calculate current 200-day SMA
        var currentSma = closes.TakeLast(200).Average();
        
        // Calculate SMA from 5 days ago
        var previousSma = closes.Skip(closes.Count - 200 - SmaLookbackDays).Take(200).Average();
        
        // Return the slope (change per day)
        return (currentSma - previousSma) / SmaLookbackDays;
    }
    
    private decimal CalculateAverageAtr(List<decimal> highs, List<decimal> lows, List<decimal> closes)
    {
        if (highs.Count < 15)
        {
            return 0m;
        }
        
        var trueRanges = new List<decimal>();
        
        for (int i = 1; i < Math.Min(highs.Count, 15); i++)
        {
            var tr1 = highs[i] - lows[i];
            var tr2 = Math.Abs(highs[i] - closes[i - 1]);
            var tr3 = Math.Abs(lows[i] - closes[i - 1]);
            
            trueRanges.Add(Math.Max(tr1, Math.Max(tr2, tr3)));
        }
        
        return trueRanges.Average();
    }
    
    private decimal CalculateReturnToDrawdownRatio(List<decimal> closes)
    {
        if (closes.Count < 2)
        {
            return 0m;
        }
        
        var peak = closes[0];
        var maxDrawdown = 0m;
        
        foreach (var close in closes)
        {
            if (close > peak)
            {
                peak = close;
            }
            
            var drawdown = (peak - close) / peak;
            if (drawdown > maxDrawdown)
            {
                maxDrawdown = drawdown;
            }
        }
        
        if (maxDrawdown == 0m)
        {
            return 10m; // No drawdown means very strong trend
        }
        
        var totalReturn = (closes.Last() - closes.First()) / closes.First();
        return Math.Abs(totalReturn) / maxDrawdown;
    }
    
    private MarketRegime ClassifyRegime(decimal smaSlope, decimal averageAtr, decimal returnToDrawdownRatio)
    {
        // High volatility regime
        if (averageAtr > VolatileAtrThreshold)
        {
            return MarketRegime.Volatile;
        }
        
        // Trending up regime
        if (smaSlope > TrendingSlopeThreshold && returnToDrawdownRatio > MinReturnToDrawdownRatio)
        {
            return MarketRegime.TrendingUp;
        }
        
        // Trending down regime
        if (smaSlope < -TrendingSlopeThreshold && returnToDrawdownRatio > MinReturnToDrawdownRatio)
        {
            return MarketRegime.TrendingDown;
        }
        
        // Default to sideways
        return MarketRegime.Sideways;
    }
    
    private decimal CalculateConfidence(decimal smaSlope, decimal averageAtr, decimal returnToDrawdownRatio)
    {
        // Simple confidence calculation based on how clear the signals are
        var slopeConfidence = Math.Min(Math.Abs(smaSlope) / TrendingSlopeThreshold, 1m);
        var atrConfidence = Math.Min(averageAtr / VolatileAtrThreshold, 1m);
        var ratioConfidence = Math.Min(returnToDrawdownRatio / MinReturnToDrawdownRatio, 1m);
        
        return (slopeConfidence + atrConfidence + ratioConfidence) / 3m;
    }
    
    public void Dispose()
    {
        _connectionMultiplexer?.Dispose();
    }
}
