using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Text.Json;
using System.Text;
using System.Net;

namespace SmaTrendFollower.Services;

/// <summary>
/// Simplified metrics service for exposing trading metrics and system health
/// Provides basic HTTP listener for monitoring and dashboards
/// </summary>
public sealed class MetricsApiService : BackgroundService, IMetricsApiService
{
    private readonly ITradingMetricsService _metricsService;
    private readonly ISystemHealthService _healthService;
    private readonly IRealTimeMarketMonitor _marketMonitor;
    private readonly ILiveSignalIntelligence _signalIntelligence;
    private readonly ILogger<MetricsApiService> _logger;
    private readonly MetricsApiConfig _config;

    private HttpListener? _httpListener;

    public MetricsApiService(
        ITradingMetricsService metricsService,
        ISystemHealthService healthService,
        IRealTimeMarketMonitor marketMonitor,
        ILiveSignalIntelligence signalIntelligence,
        ILogger<MetricsApiService> logger,
        IConfiguration configuration)
    {
        _metricsService = metricsService;
        _healthService = healthService;
        _marketMonitor = marketMonitor;
        _signalIntelligence = signalIntelligence;
        _logger = logger;
        _config = new MetricsApiConfig(
            configuration.GetValue("METRICS_API_PORT", 8080),
            configuration.GetValue("METRICS_API_ENABLE_CORS", true),
            configuration.GetValue("METRICS_API_ENABLE_SWAGGER", false)
        );
    }

    /// <summary>
    /// Background service execution
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MetricsApiService starting on port {Port}", _config.Port);

        try
        {
            _httpListener = new HttpListener();
            _httpListener.Prefixes.Add($"http://localhost:{_config.Port}/");
            _httpListener.Start();
            
            _logger.LogInformation("MetricsApiService started successfully on http://localhost:{Port}", _config.Port);

            // Process requests
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var context = await _httpListener.GetContextAsync();
                    _ = Task.Run(() => ProcessRequestAsync(context), stoppingToken);
                }
                catch (HttpListenerException)
                {
                    // Expected when stopping
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing HTTP request");
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("MetricsApiService stopping...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in MetricsApiService");
        }
        finally
        {
            _httpListener?.Stop();
            _httpListener?.Close();
            _logger.LogInformation("MetricsApiService stopped");
        }
    }

    /// <summary>
    /// Processes HTTP requests
    /// </summary>
    private async Task ProcessRequestAsync(HttpListenerContext context)
    {
        try
        {
            var request = context.Request;
            var response = context.Response;
            var path = request.Url?.AbsolutePath ?? "/";

            string responseText;
            string contentType = "application/json";

            switch (path.ToLowerInvariant())
            {
                case "/health":
                    responseText = await GetHealthResponseAsync();
                    break;
                case "/metrics":
                    responseText = await GetMetricsResponseAsync();
                    break;
                case "/live/market":
                    responseText = await GetLiveMarketResponseAsync();
                    break;
                case "/live/signals":
                    responseText = await GetLiveSignalsResponseAsync();
                    break;
                case "/metrics/prometheus":
                    responseText = await GetPrometheusResponseAsync();
                    contentType = "text/plain";
                    break;
                case "/system":
                    responseText = await GetSystemResponseAsync();
                    break;
                case "/":
                    responseText = GetApiDocumentationResponse();
                    break;
                default:
                    response.StatusCode = 404;
                    responseText = JsonSerializer.Serialize(new { error = "Not Found" });
                    break;
            }

            var buffer = Encoding.UTF8.GetBytes(responseText);
            response.ContentType = contentType;
            response.ContentLength64 = buffer.Length;
            
            // Add CORS headers if enabled
            if (_config.EnableCors)
            {
                response.Headers.Add("Access-Control-Allow-Origin", "*");
                response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
                response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
            }
            
            await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
            response.OutputStream.Close();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing request");
            try
            {
                context.Response.StatusCode = 500;
                var errorResponse = JsonSerializer.Serialize(new { error = ex.Message });
                var errorBuffer = Encoding.UTF8.GetBytes(errorResponse);
                await context.Response.OutputStream.WriteAsync(errorBuffer, 0, errorBuffer.Length);
                context.Response.OutputStream.Close();
            }
            catch
            {
                // Ignore errors when sending error response
            }
        }
    }

    /// <summary>
    /// Gets health status response
    /// </summary>
    private async Task<string> GetHealthResponseAsync()
    {
        try
        {
            var status = _healthService.GetCurrentStatus();
            var report = await _healthService.GetHealthReportAsync();

            var response = new
            {
                status = status.ToString(),
                timestamp = report.GeneratedAt,
                checks = report.Checks.ToDictionary(
                    kvp => kvp.Key,
                    kvp => new
                    {
                        status = kvp.Value.Status.ToString(),
                        details = kvp.Value.Details,
                        responseTime = kvp.Value.ResponseTime.TotalMilliseconds,
                        checkedAt = kvp.Value.CheckedAt
                    }
                ),
                recentEvents = report.RecentEvents.Take(5).Select(e => new
                {
                    component = e.Component,
                    message = e.Message,
                    severity = e.Severity.ToString(),
                    timestamp = e.Timestamp
                })
            };

            return JsonSerializer.Serialize(response);
        }
        catch (Exception ex)
        {
            return JsonSerializer.Serialize(new { error = $"Error retrieving health status: {ex.Message}" });
        }
    }

    /// <summary>
    /// Gets metrics response
    /// </summary>
    private async Task<string> GetMetricsResponseAsync()
    {
        try
        {
            var stats = await _metricsService.GetTradingStatisticsAsync();
            var kpis = _metricsService.GetKPIs();
            var performance = _metricsService.GetPerformanceMetrics();

            var response = new
            {
                trading = new
                {
                    totalTrades = stats.TotalTrades,
                    profitableTrades = stats.ProfitableTrades,
                    winRate = stats.WinRate,
                    totalPnL = stats.TotalPnL,
                    averageWin = stats.AverageWin,
                    averageLoss = stats.AverageLoss,
                    maxDrawdown = stats.MaxDrawdown,
                    sharpeRatio = stats.SharpeRatio,
                    totalSignals = stats.TotalSignals,
                    executedSignals = stats.ExecutedSignals,
                    signalExecutionRate = stats.SignalExecutionRate,
                    averageConfidence = stats.AverageConfidence,
                    generatedAt = stats.GeneratedAt
                },
                kpis = kpis,
                performance = performance.ToDictionary(
                    kvp => kvp.Key,
                    kvp => new
                    {
                        operation = kvp.Value.Operation,
                        duration = kvp.Value.Duration.TotalMilliseconds,
                        success = kvp.Value.Success,
                        details = kvp.Value.Details,
                        callCount = kvp.Value.CallCount,
                        timestamp = kvp.Value.Timestamp
                    }
                )
            };

            return JsonSerializer.Serialize(response);
        }
        catch (Exception ex)
        {
            return JsonSerializer.Serialize(new { error = $"Error retrieving metrics: {ex.Message}" });
        }
    }

    /// <summary>
    /// Gets live market data response
    /// </summary>
    private Task<string> GetLiveMarketResponseAsync()
    {
        try
        {
            var snapshots = _marketMonitor.GetAllMarketSnapshots();
            var alerts = _marketMonitor.GetRecentAlerts(10);

            var response = new
            {
                snapshots = snapshots.ToDictionary(
                    kvp => kvp.Key,
                    kvp => new
                    {
                        symbol = kvp.Value.Symbol,
                        currentPrice = kvp.Value.CurrentPrice,
                        priceChange = kvp.Value.PriceChange,
                        trend = kvp.Value.Trend.ToString(),
                        volatility = kvp.Value.Volatility,
                        lastUpdated = kvp.Value.LastUpdated
                    }
                ),
                alerts = alerts.Select(a => new
                {
                    symbol = a.Symbol,
                    alertType = a.AlertType.ToString(),
                    description = a.Description,
                    price = a.Price,
                    severity = a.Severity.ToString(),
                    timestamp = a.Timestamp
                })
            };

            return Task.FromResult(JsonSerializer.Serialize(response));
        }
        catch (Exception ex)
        {
            return Task.FromResult(JsonSerializer.Serialize(new { error = $"Error retrieving live market data: {ex.Message}" }));
        }
    }

    /// <summary>
    /// Gets live signals response
    /// </summary>
    private Task<string> GetLiveSignalsResponseAsync()
    {
        try
        {
            var signals = _signalIntelligence.GetLiveSignals();
            var signalStates = _signalIntelligence.GetAllSignalStates();

            var response = new
            {
                signals = signals.Select(s => new
                {
                    symbol = s.Symbol,
                    price = s.Price,
                    atr = s.Atr,
                    sixMonthReturn = s.SixMonthReturn,
                    intelligenceScore = s.IntelligenceScore,
                    confidence = s.Confidence,
                    analysisMode = s.AnalysisMode.ToString(),
                    marketTrend = s.MarketTrend.ToString(),
                    volatility = s.Volatility,
                    reasoning = s.Reasoning,
                    generatedAt = s.GeneratedAt
                }),
                signalStates = signalStates.ToDictionary(
                    kvp => kvp.Key,
                    kvp => new
                    {
                        symbol = kvp.Value.Symbol,
                        status = kvp.Value.Status.ToString(),
                        lastUpdated = kvp.Value.LastUpdated,
                        updateCount = kvp.Value.Updates.Count
                    }
                )
            };

            return Task.FromResult(JsonSerializer.Serialize(response));
        }
        catch (Exception ex)
        {
            return Task.FromResult(JsonSerializer.Serialize(new { error = $"Error retrieving live signals: {ex.Message}" }));
        }
    }

    /// <summary>
    /// Gets Prometheus metrics response
    /// </summary>
    private async Task<string> GetPrometheusResponseAsync()
    {
        try
        {
            var stats = await _metricsService.GetTradingStatisticsAsync();
            var kpis = _metricsService.GetKPIs();
            var healthStatus = _healthService.GetCurrentStatus();

            var prometheus = new StringBuilder();
            
            // Trading metrics
            prometheus.AppendLine($"# HELP trading_total_trades Total number of trades executed");
            prometheus.AppendLine($"# TYPE trading_total_trades counter");
            prometheus.AppendLine($"trading_total_trades {stats.TotalTrades}");
            
            prometheus.AppendLine($"# HELP trading_win_rate Win rate percentage");
            prometheus.AppendLine($"# TYPE trading_win_rate gauge");
            prometheus.AppendLine($"trading_win_rate {stats.WinRate}");
            
            prometheus.AppendLine($"# HELP trading_total_pnl Total profit and loss");
            prometheus.AppendLine($"# TYPE trading_total_pnl gauge");
            prometheus.AppendLine($"trading_total_pnl {stats.TotalPnL}");
            
            prometheus.AppendLine($"# HELP trading_sharpe_ratio Sharpe ratio");
            prometheus.AppendLine($"# TYPE trading_sharpe_ratio gauge");
            prometheus.AppendLine($"trading_sharpe_ratio {stats.SharpeRatio}");

            // System health
            prometheus.AppendLine($"# HELP system_health_status System health status (0=Unknown, 1=Healthy, 2=Degraded, 3=Unhealthy)");
            prometheus.AppendLine($"# TYPE system_health_status gauge");
            prometheus.AppendLine($"system_health_status {(int)healthStatus}");

            // KPIs
            foreach (var kpi in kpis)
            {
                var metricName = $"kpi_{kpi.Key.Replace("-", "_").Replace(".", "_")}";
                prometheus.AppendLine($"# HELP {metricName} KPI metric: {kpi.Key}");
                prometheus.AppendLine($"# TYPE {metricName} gauge");
                prometheus.AppendLine($"{metricName} {kpi.Value}");
            }

            return prometheus.ToString();
        }
        catch (Exception ex)
        {
            return $"# Error generating Prometheus metrics: {ex.Message}";
        }
    }

    /// <summary>
    /// Gets system info response
    /// </summary>
    private Task<string> GetSystemResponseAsync()
    {
        try
        {
            var process = System.Diagnostics.Process.GetCurrentProcess();
            var version = typeof(Program).Assembly.GetName().Version?.ToString() ?? "Unknown";

            var response = new
            {
                version = version,
                environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production",
                processId = Environment.ProcessId,
                machineName = Environment.MachineName,
                osVersion = Environment.OSVersion.ToString(),
                dotNetVersion = Environment.Version.ToString(),
                startTime = process.StartTime,
                uptime = DateTime.Now - process.StartTime,
                memoryUsageMB = process.WorkingSet64 / 1024 / 1024,
                threadCount = process.Threads.Count,
                timestamp = DateTime.UtcNow
            };

            return Task.FromResult(JsonSerializer.Serialize(response));
        }
        catch (Exception ex)
        {
            return Task.FromResult(JsonSerializer.Serialize(new { error = $"Error retrieving system info: {ex.Message}" }));
        }
    }

    /// <summary>
    /// Gets API documentation response
    /// </summary>
    private string GetApiDocumentationResponse()
    {
        var apiDocs = new
        {
            name = "SmaTrendFollower Metrics API",
            version = "1.0.0",
            description = "REST API for trading metrics and system monitoring",
            endpoints = new
            {
                health = "/health - System health status",
                metrics = "/metrics - Trading metrics and performance",
                liveMarket = "/live/market - Live market data and alerts",
                liveSignals = "/live/signals - Live trading signals",
                prometheus = "/metrics/prometheus - Prometheus format metrics",
                system = "/system - System information"
            },
            timestamp = DateTime.UtcNow
        };

        return JsonSerializer.Serialize(apiDocs);
    }

    public override void Dispose()
    {
        _httpListener?.Stop();
        _httpListener?.Close();
        base.Dispose();
    }
}

/// <summary>
/// Interface for metrics API service
/// </summary>
public interface IMetricsApiService
{
    // Service interface - implementation handles HTTP endpoints
}

/// <summary>
/// Configuration for metrics API service
/// </summary>
public record MetricsApiConfig(
    int Port = 8080,
    bool EnableCors = true,
    bool EnableSwagger = false
);
